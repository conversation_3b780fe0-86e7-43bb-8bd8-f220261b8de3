<template>
  <el-dialog
    :model-value="visible"
    @update:model-value="$emit('update:visible', $event)"
    title="批量导入员工"
    width="800px"
    :close-on-click-modal="false"
  >
    <div class="import-container">
      <!-- 步骤指示器 -->
      <el-steps :active="currentStep" finish-status="success" align-center>
        <el-step title="选择文件" />
        <el-step title="数据预览" />
        <el-step title="导入结果" />
      </el-steps>

      <!-- 步骤1: 文件上传 -->
      <div v-if="currentStep === 0" class="step-content">
        <div class="upload-section">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            drag
            :auto-upload="false"
            :on-change="handleFileChange"
            :before-upload="beforeUpload"
            accept=".xlsx,.xls"
            :limit="1"
            :file-list="fileList"
          >
            <el-icon class="el-icon--upload"><UploadFilled /></el-icon>
            <div class="el-upload__text">
              将Excel文件拖到此处，或<em>点击上传</em>
            </div>
            <template #tip>
              <div class="el-upload__tip">
                只能上传 .xlsx/.xls 文件，且不超过 10MB
              </div>
            </template>
          </el-upload>
        </div>

        <div class="format-info">
          <div class="format-header">
            <h4>Excel文件格式要求：</h4>
            <el-button type="primary" size="small" @click="downloadTemplate">
              <el-icon><Download /></el-icon>
              下载模板
            </el-button>
          </div>
          <p>第一行为表头，包含以下字段（顺序必须一致）：</p>
          <div class="field-list">
            <el-tag v-for="field in requiredFields" :key="field" type="info" class="field-tag">
              {{ field }}
            </el-tag>
          </div>
          <p class="note">
            <el-icon><InfoFilled /></el-icon>
            注意：密码字段可以为空，系统将自动设置为默认密码"123456"
          </p>
        </div>
      </div>

      <!-- 步骤2: 数据预览 -->
      <div v-if="currentStep === 1" class="step-content">
        <div class="preview-info">
          <el-alert
            :title="`共解析到 ${previewData.length} 条员工数据`"
            type="info"
            :closable="false"
            show-icon
          />
        </div>

        <div class="preview-table" v-if="previewData.length > 0">
          <el-table
            :data="previewData.slice(0, 10)"
            style="width: 100%"
            max-height="400"
            border
          >
            <el-table-column prop="name" label="姓名" width="80" />
            <el-table-column prop="username" label="用户名" width="100" />
            <el-table-column prop="position" label="职位" width="100" />
            <el-table-column prop="departmentId" label="部门ID" width="80" />
            <el-table-column prop="phone" label="公司电话" width="120" />
            <el-table-column prop="avatar" label="手机" width="120" />
            <el-table-column prop="email" label="邮箱" width="150" />
          </el-table>
          <p v-if="previewData.length > 10" class="preview-note">
            仅显示前10条数据预览，实际将导入 {{ previewData.length }} 条数据
          </p>
        </div>
      </div>

      <!-- 步骤3: 导入结果 -->
      <div v-if="currentStep === 2" class="step-content">
        <div class="result-summary">
          <el-result
            :icon="importResult.success ? 'success' : 'error'"
            :title="importResult.success ? '导入完成' : '导入失败'"
            :sub-title="importResult.message"
          >
            <template #extra>
              <div class="result-stats">
                <el-statistic
                  title="成功导入"
                  :value="importResult.successCount"
                  suffix="条"
                  class="stat-item success"
                />
                <el-statistic
                  title="导入失败"
                  :value="importResult.failureCount"
                  suffix="条"
                  class="stat-item error"
                />
              </div>
            </template>
          </el-result>
        </div>

        <!-- 失败详情 -->
        <div v-if="importResult.failures && importResult.failures.length > 0" class="failure-details">
          <h4>失败详情：</h4>
          <el-table
            :data="importResult.failures"
            style="width: 100%"
            max-height="300"
            border
          >
            <el-table-column prop="row" label="行号" width="80" />
            <el-table-column prop="name" label="姓名" width="100" />
            <el-table-column prop="username" label="用户名" width="120" />
            <el-table-column prop="error" label="失败原因" />
          </el-table>
        </div>
      </div>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">
          {{ currentStep === 2 ? '关闭' : '取消' }}
        </el-button>
        <el-button
          v-if="currentStep === 0"
          type="primary"
          :disabled="!selectedFile"
          @click="parseExcelFile"
          :loading="parsing"
        >
          {{ parsing ? '解析中...' : '解析文件' }}
        </el-button>
        <el-button
          v-if="currentStep === 1"
          @click="currentStep = 0"
        >
          上一步
        </el-button>
        <el-button
          v-if="currentStep === 1"
          type="primary"
          :disabled="previewData.length === 0"
          @click="importData"
          :loading="importing"
        >
          {{ importing ? '导入中...' : '开始导入' }}
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive } from 'vue'
import { ElMessage } from 'element-plus'
import { UploadFilled, InfoFilled, Download } from '@element-plus/icons-vue'
import * as XLSX from 'xlsx'
import request from '@/utils/request'

const props = defineProps({
  visible: Boolean
})

const emit = defineEmits(['update:visible', 'success'])

// 状态管理
const currentStep = ref(0)
const selectedFile = ref(null)
const fileList = ref([])
const parsing = ref(false)
const importing = ref(false)
const previewData = ref([])
const importResult = reactive({
  success: false,
  message: '',
  successCount: 0,
  failureCount: 0,
  failures: []
})

// 必需字段
const requiredFields = [
  '姓名', '用户名', '密码', '职位', '部门ID', 
  '公司电话', '公司名称', '地址', '手机', '传真', '邮编', '邮箱'
]

// 字段映射关系
const fieldMapping = {
  '姓名': 'name',
  '用户名': 'username',
  '密码': 'password',
  '职位': 'position',
  '部门ID': 'departmentId',
  '公司电话': 'phone',
  '公司名称': 'workLocation',
  '地址': 'address',
  '手机': 'avatar',
  '传真': 'employeeId',
  '邮编': 'manager',
  '邮箱': 'email'
}

// 文件上传前验证
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' ||
                  file.type === 'application/vnd.ms-excel'
  const isLt10M = file.size / 1024 / 1024 < 10

  if (!isExcel) {
    ElMessage.error('只能上传Excel文件!')
    return false
  }
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过10MB!')
    return false
  }
  return false // 阻止自动上传
}

// 文件选择处理
const handleFileChange = (file) => {
  selectedFile.value = file.raw
  fileList.value = [file]
}

// 解析Excel文件
const parseExcelFile = async () => {
  if (!selectedFile.value) {
    ElMessage.error('请先选择文件')
    return
  }

  parsing.value = true
  try {
    const data = await readExcelFile(selectedFile.value)
    if (data && data.length > 0) {
      previewData.value = data
      currentStep.value = 1
      ElMessage.success(`成功解析 ${data.length} 条数据`)
    } else {
      ElMessage.error('文件中没有找到有效数据')
    }
  } catch (error) {
    console.error('解析Excel文件失败:', error)
    ElMessage.error('解析文件失败: ' + error.message)
  } finally {
    parsing.value = false
  }
}

// 读取Excel文件
const readExcelFile = (file) => {
  return new Promise((resolve, reject) => {
    const reader = new FileReader()
    reader.onload = (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const sheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[sheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })

        if (jsonData.length < 2) {
          reject(new Error('Excel文件至少需要包含表头和一行数据'))
          return
        }

        const headers = jsonData[0]
        const expectedHeaders = requiredFields

        // 验证表头
        if (!validateHeaders(headers, expectedHeaders)) {
          reject(new Error('Excel表头格式不正确，请检查字段名称和顺序'))
          return
        }

        // 转换数据
        const employees = []
        for (let i = 1; i < jsonData.length; i++) {
          const row = jsonData[i]
          if (row.some(cell => cell !== undefined && cell !== '')) {
            const employee = convertRowToEmployee(row, headers)
            if (employee) {
              employees.push(employee)
            }
          }
        }

        resolve(employees)
      } catch (error) {
        reject(error)
      }
    }
    reader.onerror = () => reject(new Error('读取文件失败'))
    reader.readAsArrayBuffer(file)
  })
}

// 验证表头
const validateHeaders = (actualHeaders, expectedHeaders) => {
  if (actualHeaders.length < expectedHeaders.length) {
    return false
  }

  for (let i = 0; i < expectedHeaders.length; i++) {
    if (actualHeaders[i] !== expectedHeaders[i]) {
      return false
    }
  }
  return true
}

// 转换行数据为员工对象
const convertRowToEmployee = (row, headers) => {
  const employee = {}

  headers.forEach((header, index) => {
    const fieldName = fieldMapping[header]
    if (fieldName && row[index] !== undefined) {
      let value = row[index]

      // 处理特殊字段
      if (fieldName === 'departmentId') {
        value = value ? parseInt(value) : null
      } else if (fieldName === 'password') {
        value = value || '123456' // 默认密码
      } else {
        value = value ? String(value).trim() : ''
      }

      employee[fieldName] = value
    }
  })

  // 验证必填字段
  if (!employee.name || !employee.username) {
    return null
  }

  return employee
}

// 导入数据
const importData = async () => {
  if (previewData.value.length === 0) {
    ElMessage.error('没有可导入的数据')
    return
  }

  importing.value = true
  try {
    const response = await request.post('/employees/batch-import', {
      employees: previewData.value
    })

    importResult.success = true
    importResult.message = response.message || '批量导入完成'
    importResult.successCount = response.data?.successCount || 0
    importResult.failureCount = response.data?.failureCount || 0
    importResult.failures = response.data?.failures || []

    currentStep.value = 2

    if (importResult.failureCount === 0) {
      ElMessage.success('所有数据导入成功!')
    } else {
      ElMessage.warning(`导入完成，成功${importResult.successCount}条，失败${importResult.failureCount}条`)
    }
  } catch (error) {
    console.error('批量导入失败:', error)
    importResult.success = false
    importResult.message = error.response?.data?.message || '导入失败'
    importResult.successCount = 0
    importResult.failureCount = previewData.value.length
    importResult.failures = []
    currentStep.value = 2
    ElMessage.error('批量导入失败: ' + importResult.message)
  } finally {
    importing.value = false
  }
}

// 取消/关闭处理
const handleCancel = () => {
  if (currentStep.value === 2 && importResult.success) {
    emit('success')
  } else {
    emit('update:visible', false)
  }
  resetDialog()
}

// 重置对话框状态
const resetDialog = () => {
  currentStep.value = 0
  selectedFile.value = null
  fileList.value = []
  previewData.value = []
  Object.assign(importResult, {
    success: false,
    message: '',
    successCount: 0,
    failureCount: 0,
    failures: []
  })
}

// 下载Excel模板
const downloadTemplate = () => {
  // 创建模板数据
  const templateData = [
    requiredFields, // 表头
    ['张三', 'zhangsan', '123456', '软件工程师', '1', '010-12345678', '北京分公司', '北京市朝阳区', '13800138000', '010-87654321', '100000', '<EMAIL>'],
    ['李四', 'lisi', '', '产品经理', '2', '021-12345678', '上海分公司', '上海市浦东新区', '13900139000', '021-87654321', '200000', '<EMAIL>']
  ]

  // 创建工作簿
  const ws = XLSX.utils.aoa_to_sheet(templateData)
  const wb = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(wb, ws, '员工导入模板')

  // 设置列宽
  const colWidths = [
    { wch: 10 }, // 姓名
    { wch: 15 }, // 用户名
    { wch: 10 }, // 密码
    { wch: 15 }, // 职位
    { wch: 10 }, // 部门ID
    { wch: 15 }, // 公司电话
    { wch: 15 }, // 公司名称
    { wch: 20 }, // 地址
    { wch: 15 }, // 手机
    { wch: 15 }, // 传真
    { wch: 10 }, // 邮编
    { wch: 20 }  // 邮箱
  ]
  ws['!cols'] = colWidths

  // 下载文件
  XLSX.writeFile(wb, '员工批量导入模板.xlsx')
  ElMessage.success('模板下载成功')
}
</script>

<style scoped>
.import-container {
  padding: 20px 0;
}

.step-content {
  margin-top: 30px;
  min-height: 400px;
}

.upload-section {
  margin-bottom: 30px;
}

.format-info {
  background: #f8f9fa;
  padding: 20px;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.format-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.format-info h4 {
  margin: 0;
  color: #303133;
}

.field-list {
  margin: 15px 0;
}

.field-tag {
  margin: 2px 5px 2px 0;
}

.note {
  margin: 15px 0 0 0;
  color: #909399;
  display: flex;
  align-items: center;
  gap: 5px;
}

.preview-info {
  margin-bottom: 20px;
}

.preview-table {
  margin-top: 20px;
}

.preview-note {
  margin-top: 10px;
  color: #909399;
  text-align: center;
}

.result-stats {
  display: flex;
  gap: 40px;
  justify-content: center;
  margin-top: 20px;
}

.stat-item.success :deep(.el-statistic__number) {
  color: #67c23a;
}

.stat-item.error :deep(.el-statistic__number) {
  color: #f56c6c;
}

.failure-details {
  margin-top: 30px;
}

.failure-details h4 {
  margin-bottom: 15px;
  color: #f56c6c;
}

.dialog-footer {
  text-align: right;
}
</style>
