package com.minmetals.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * 员工批量导入请求DTO
 *
 * <AUTHOR>
 * @version 1.0.0
 */
@Data
@Schema(description = "员工批量导入请求")
public class EmployeeBatchImportRequest {

    @Schema(description = "员工列表", required = true)
    @NotEmpty(message = "员工列表不能为空")
    @Valid
    private List<EmployeeCreateRequest> employees;
}
