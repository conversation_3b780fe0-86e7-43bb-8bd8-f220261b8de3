{"name": "ssf", "version": "0.11.2", "author": "sheetjs", "description": "Format data using ECMA-376 spreadsheet Format Codes", "keywords": ["format", "sprintf", "spreadsheet"], "main": "./ssf", "types": "types", "dependencies": {"frac": "~1.1.2"}, "devDependencies": {"@sheetjs/uglify-js": "~2.7.3", "@types/node": "^8.0.7", "blanket": "~1.2.3", "dtslint": "^0.1.2", "mocha": "~2.5.3", "typescript": "2.2.0"}, "repository": {"type": "git", "url": "git://github.com/SheetJS/ssf.git"}, "scripts": {"test": "make test", "build": "make", "lint": "make fullint", "dtslint": "dtslint types"}, "config": {"blanket": {"pattern": "ssf.js"}}, "alex": {"allow": ["special", "simple", "just", "crash", "wtf", "holes"]}, "homepage": "http://sheetjs.com/", "bugs": {"url": "https://github.com/SheetJS/ssf/issues"}, "license": "Apache-2.0", "engines": {"node": ">=0.8"}}