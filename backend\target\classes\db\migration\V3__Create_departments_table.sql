-- 创建部门表
CREATE TABLE departments (
    id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
    name VARCHAR(100) NOT NULL COMMENT '部门名称',
    code VARCHAR(50) NOT NULL UNIQUE COMMENT '部门编码',
    parent_id BIGINT COMMENT '父部门ID',
    level INT NOT NULL COMMENT '部门级别（1-一级部门，2-二级部门，3-三级部门）',
    path VARCHAR(500) COMMENT '部门路径',
    description VARCHAR(500) COMMENT '部门描述',
    manager VARCHAR(50) COMMENT '部门负责人',
    phone VARCHAR(20) COMMENT '联系电话',
    email VARCHAR(100) COMMENT '联系邮箱',
    address VARCHAR(200) COMMENT '办公地址',
    sort_order INT DEFAULT 0 COMMENT '排序号',
    enabled BOOLEAN NOT NULL DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_parent_id (parent_id),
    INDEX idx_level (level),
    INDEX idx_enabled (enabled),
    INDEX idx_code (code),
    INDEX idx_path (path),
    
    FOREIGN KEY (parent_id) REFERENCES departments(id) ON DELETE RESTRICT
) COMMENT='部门表';

-- 为员工表添加部门ID字段
ALTER TABLE employees ADD COLUMN department_id BIGINT COMMENT '部门ID';
ALTER TABLE employees ADD INDEX idx_department_id (department_id);

-- 插入示例部门数据
INSERT INTO departments (name, code, parent_id, level, path, description, manager, sort_order, enabled) VALUES
-- 一级部门
('技术部', 'TECH', NULL, 1, '/1', '负责公司技术研发和系统维护', '张技术', 1, TRUE),
('市场部', 'MARKET', NULL, 1, '/2', '负责市场推广和销售', '李市场', 2, TRUE),
('人事部', 'HR', NULL, 1, '/3', '负责人力资源管理', '王人事', 3, TRUE),
('财务部', 'FINANCE', NULL, 1, '/4', '负责财务管理和会计核算', '赵财务', 4, TRUE),
('运营部', 'OPERATION', NULL, 1, '/5', '负责日常运营管理', '刘运营', 5, TRUE);

-- 更新部门路径包含自己的ID
UPDATE departments SET path = CONCAT('/', id) WHERE parent_id IS NULL;

-- 二级部门
INSERT INTO departments (name, code, parent_id, level, path, description, manager, sort_order, enabled) VALUES
-- 技术部下的二级部门
('前端开发部', 'TECH_FE', 1, 2, '/1/6', '负责前端技术开发', '前端主管', 1, TRUE),
('后端开发部', 'TECH_BE', 1, 2, '/1/7', '负责后端技术开发', '后端主管', 2, TRUE),
('测试部', 'TECH_QA', 1, 2, '/1/8', '负责软件测试', '测试主管', 3, TRUE),

-- 市场部下的二级部门
('销售部', 'MARKET_SALES', 2, 2, '/2/9', '负责产品销售', '销售主管', 1, TRUE),
('推广部', 'MARKET_PROMO', 2, 2, '/2/10', '负责市场推广', '推广主管', 2, TRUE),

-- 人事部下的二级部门
('招聘部', 'HR_RECRUIT', 3, 2, '/3/11', '负责人员招聘', '招聘主管', 1, TRUE),
('培训部', 'HR_TRAIN', 3, 2, '/3/12', '负责员工培训', '培训主管', 2, TRUE);

-- 三级部门
INSERT INTO departments (name, code, parent_id, level, path, description, manager, sort_order, enabled) VALUES
-- 前端开发部下的三级部门
('Vue开发组', 'TECH_FE_VUE', 6, 3, '/1/6/13', '负责Vue项目开发', 'Vue组长', 1, TRUE),
('React开发组', 'TECH_FE_REACT', 6, 3, '/1/6/14', '负责React项目开发', 'React组长', 2, TRUE),

-- 后端开发部下的三级部门
('Java开发组', 'TECH_BE_JAVA', 7, 3, '/1/7/15', '负责Java项目开发', 'Java组长', 1, TRUE),
('Python开发组', 'TECH_BE_PYTHON', 7, 3, '/1/7/16', '负责Python项目开发', 'Python组长', 2, TRUE),

-- 销售部下的三级部门
('企业销售组', 'MARKET_SALES_B2B', 9, 3, '/2/9/17', '负责企业客户销售', 'B2B组长', 1, TRUE),
('个人销售组', 'MARKET_SALES_B2C', 9, 3, '/2/9/18', '负责个人客户销售', 'B2C组长', 2, TRUE);
